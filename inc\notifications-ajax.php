<?php
/**
 * معالجة طلبات AJAX لنظام الإشعارات
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * حذف إشعار واحد
 */
function sekai_ajax_delete_notification() {
    // التحقق من nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekai_ajax_nonce')) {
        wp_send_json_error(array('message' => 'خطأ في التحقق من الأمان'));
    }
    
    // التحقق من تسجيل الدخول
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'يجب تسجيل الدخول أولاً'));
    }
    
    // التحقق من معرف الإشعار
    if (!isset($_POST['notification_id']) || !is_numeric($_POST['notification_id'])) {
        wp_send_json_error(array('message' => 'معرف الإشعار غير صالح'));
    }
    
    $notification_id = intval($_POST['notification_id']);
    $user_id = get_current_user_id();
    
    // التحقق من ملكية الإشعار
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_notifications';
    $notification = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
        $notification_id,
        $user_id
    ));
    
    if (!$notification) {
        wp_send_json_error(array('message' => 'لا يمكن العثور على الإشعار أو ليس لديك صلاحية حذفه'));
    }
    
    // حذف الإشعار
    $result = sekai_delete_notification($notification_id);
    
    if ($result) {
        wp_send_json_success(array('message' => 'تم حذف الإشعار بنجاح'));
    } else {
        wp_send_json_error(array('message' => 'فشل في حذف الإشعار'));
    }
}
add_action('wp_ajax_delete_notification', 'sekai_ajax_delete_notification');

/**
 * حذف جميع الإشعارات
 */
function sekai_ajax_delete_all_notifications() {
    // التحقق من nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekai_ajax_nonce')) {
        wp_send_json_error(array('message' => 'خطأ في التحقق من الأمان'));
    }
    
    // التحقق من تسجيل الدخول
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'يجب تسجيل الدخول أولاً'));
    }
    
    $user_id = get_current_user_id();
    
    // حذف جميع الإشعارات
    $result = sekai_delete_all_user_notifications($user_id);
    
    if ($result) {
        wp_send_json_success(array('message' => 'تم حذف جميع الإشعارات بنجاح'));
    } else {
        wp_send_json_error(array('message' => 'فشل في حذف الإشعارات'));
    }
}
add_action('wp_ajax_delete_all_notifications', 'sekai_ajax_delete_all_notifications');

/**
 * تحديث حالة قراءة الإشعار
 */
function sekai_ajax_mark_notification_as_read() {
    // التحقق من nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekai_ajax_nonce')) {
        wp_send_json_error(array('message' => 'خطأ في التحقق من الأمان'));
    }
    
    // التحقق من تسجيل الدخول
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'يجب تسجيل الدخول أولاً'));
    }
    
    // التحقق من معرف الإشعار
    if (!isset($_POST['notification_id']) || !is_numeric($_POST['notification_id'])) {
        wp_send_json_error(array('message' => 'معرف الإشعار غير صالح'));
    }
    
    $notification_id = intval($_POST['notification_id']);
    $user_id = get_current_user_id();
    
    // التحقق من ملكية الإشعار
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_notifications';
    $notification = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
        $notification_id,
        $user_id
    ));
    
    if (!$notification) {
        wp_send_json_error(array('message' => 'لا يمكن العثور على الإشعار أو ليس لديك صلاحية تحديثه'));
    }
    
    // تحديث حالة القراءة
    $result = sekai_mark_notification_as_read($notification_id);
    
    if ($result) {
        wp_send_json_success(array('message' => 'تم تحديث حالة الإشعار بنجاح'));
    } else {
        wp_send_json_error(array('message' => 'فشل في تحديث حالة الإشعار'));
    }
}
add_action('wp_ajax_mark_notification_as_read', 'sekai_ajax_mark_notification_as_read');

/**
 * الحصول على عدد الإشعارات غير المقروءة
 */
function sekai_ajax_get_unread_count() {
    // التحقق من nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekai_ajax_nonce')) {
        wp_send_json_error(array('message' => 'خطأ في التحقق من الأمان'));
    }
    
    // التحقق من تسجيل الدخول
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'يجب تسجيل الدخول أولاً'));
    }
    
    $user_id = get_current_user_id();
    $count = sekai_get_unread_notifications_count($user_id);
    
    wp_send_json_success(array('count' => $count));
}
add_action('wp_ajax_get_unread_notifications_count', 'sekai_ajax_get_unread_count');
