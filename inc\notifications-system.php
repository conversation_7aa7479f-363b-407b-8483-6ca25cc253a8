<?php
/**
 * نظام الإشعارات لموقع سيكاي بلس - نسخة محسنة وموثوقة
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * إنشاء جدول الإشعارات عند تفعيل القالب
 */
function sekai_create_notifications_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        title varchar(255) DEFAULT '',
        message text NOT NULL,
        type varchar(50) NOT NULL,
        is_read tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY user_id (user_id),
        KEY is_read (is_read)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
add_action('after_switch_theme', 'sekai_create_notifications_table');

/**
 * إضافة إشعار جديد
 */
function sekai_add_notification($user_id, $type, $message, $title = '') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';

    // تعقيم وتأكيد القيم
    $user_id = intval($user_id);
    $type    = sanitize_text_field($type);
    $message = sanitize_textarea_field($message);
    $title   = sanitize_text_field($title);

    if (!$user_id || !$type || !$message) {
        error_log("sekai_add_notification: بيانات ناقصة user_id:$user_id type:$type message:$message");
        return false;
    }

    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id'    => $user_id,
            'title'      => $title,
            'message'    => $message,
            'type'       => $type,
            'is_read'    => 0,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s', '%d', '%s')
    );

    if ($result) {
        return $wpdb->insert_id;
    } else {
        error_log('sekai_add_notification فشل الإدخال: ' . $wpdb->last_error);
        return false;
    }
}

/**
 * جلب إشعارات المستخدم (مع إمكانية جلب غير المقروء فقط)
 */
function sekai_get_user_notifications($user_id, $limit = 10, $offset = 0, $only_unread = false) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';

    $user_id = intval($user_id);
    $limit   = intval($limit);
    $offset  = intval($offset);

    $where = "user_id = %d";
    $params = array($user_id);

    if ($only_unread) {
        $where .= " AND is_read = 0";
    }

    $sql = "SELECT * FROM $table_name WHERE $where ORDER BY created_at DESC LIMIT %d OFFSET %d";
    $params[] = $limit;
    $params[] = $offset;

    $query = $wpdb->prepare($sql, ...$params);
    return $wpdb->get_results($query);
}

/**
 * تحديث حالة قراءة الإشعار
 */
function sekai_mark_notification_as_read($notification_id, $is_read = true) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $notification_id = intval($notification_id);

    $result = $wpdb->update(
        $table_name,
        array('is_read' => $is_read ? 1 : 0),
        array('id' => $notification_id),
        array('%d'),
        array('%d')
    );
    return $result !== false;
}

/**
 * تحديث حالة جميع إشعارات المستخدم
 */
function sekai_mark_all_notifications_as_read($user_id, $is_read = true) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $user_id = intval($user_id);

    $result = $wpdb->update(
        $table_name,
        array('is_read' => $is_read ? 1 : 0),
        array('user_id' => $user_id),
        array('%d'),
        array('%d')
    );
    return $result !== false;
}

/**
 * حذف إشعار
 */
function sekai_delete_notification($notification_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $notification_id = intval($notification_id);

    $result = $wpdb->delete(
        $table_name,
        array('id' => $notification_id),
        array('%d')
    );
    return $result !== false;
}

/**
 * حذف جميع إشعارات المستخدم
 */
function sekai_delete_all_user_notifications($user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $user_id = intval($user_id);

    $result = $wpdb->delete(
        $table_name,
        array('user_id' => $user_id),
        array('%d')
    );
    return $result !== false;
}

/**
 * عدد الإشعارات غير المقروءة للمستخدم
 */
function sekai_get_unread_notifications_count($user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'sekaiplus_user_notifications';
    $user_id = intval($user_id);

    $query = $wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND is_read = 0",
        $user_id
    );
    return (int) $wpdb->get_var($query);
}

/**
 * إرسال إشعار عند إضافة فصل جديد لرواية
 */
function sekai_notify_new_chapter($post_id, $post) {
    if ($post->post_type !== 'chapter' || $post->post_status !== 'publish') return;

    $novel_id = get_post_meta($post_id, '_novel_id', true);
    if (!$novel_id) return;

    $chapter_number = get_post_meta($post_id, '_chapter_number', true);

    // تحقق من أن الفصل ليس ترجمة مكررة
    $existing_chapters = get_posts(array(
        'post_type'      => 'chapter',
        'post_status'    => 'publish',
        'posts_per_page' => -1,
        'meta_query'     => array(
            array('key' => '_novel_id', 'value' => $novel_id, 'compare' => '='),
            array('key' => '_chapter_number', 'value' => $chapter_number, 'compare' => '=')
        ),
        'fields'         => 'ids'
    ));
    if (count($existing_chapters) > 1) {
        $oldest_chapter = get_posts(array(
            'post_type'      => 'chapter',
            'post_status'    => 'publish',
            'posts_per_page' => 1,
            'meta_query'     => array(
                array('key' => '_novel_id', 'value' => $novel_id, 'compare' => '='),
                array('key' => '_chapter_number', 'value' => $chapter_number, 'compare' => '=')
            ),
            'orderby'        => 'date',
            'order'          => 'ASC',
            'fields'         => 'ids'
        ));
        if (!empty($oldest_chapter) && $oldest_chapter[0] != $post_id) return;
    }

    global $wpdb;
    $users_with_bookmark = array();
    $all_users = $wpdb->get_results(
        "SELECT user_id, meta_value FROM $wpdb->usermeta WHERE meta_key = '_novel_bookmarks'"
    );
    foreach ($all_users as $user) {
        $bookmarks = json_decode($user->meta_value, true);
        if (is_array($bookmarks) && in_array($novel_id, $bookmarks)) {
            $users_with_bookmark[] = $user->user_id;
        }
    }
    if (empty($users_with_bookmark)) return;

    $novel_title   = get_the_title($novel_id);
    $chapter_title = get_the_title($post_id);

    foreach ($users_with_bookmark as $user_id) {
        $message = sprintf('تم إضافة فصل جديد "%s" للرواية "%s"', $chapter_title, $novel_title);
        $title = 'فصل جديد متاح!';
        sekai_add_notification($user_id, 'new_chapter', $message, $title);
    }
}
add_action('wp_insert_post', 'sekai_notify_new_chapter', 10, 2);

/**
 * إشعار للمترجم عند الموافقة على المحتوى
 */
function sekai_notify_content_approval($new_status, $old_status, $post) {
    if ($old_status !== 'pending' || $new_status !== 'publish') return;
    if (!in_array($post->post_type, array('novel', 'chapter'))) return;

    $author_id     = $post->post_author;
    $content_type  = $post->post_type === 'novel' ? 'الرواية' : 'الفصل';
    $content_title = get_the_title($post->ID);

    $message = sprintf('تمت الموافقة على %s "%s" ونشره', $content_type, $content_title);
    $title = 'تمت الموافقة على المحتوى!';
    sekai_add_notification($author_id, 'approved_content', $message, $title);

    // إشعار المستخدمين في حال كان المحتوى فصلًا
    if ($post->post_type === 'chapter') {
        $novel_id = get_post_meta($post->ID, '_novel_id', true);
        if ($novel_id) sekai_notify_users_about_new_chapter($post->ID, $novel_id);
    }
}
// تفعيل إشعارات قبول المحتوى
add_action('transition_post_status', 'sekai_notify_content_approval', 10, 3);

/**
 * إشعار المستخدمين الذين أضافوا الرواية للمفضلة عند إضافة فصل جديد
 */
function sekai_notify_users_about_new_chapter($chapter_id, $novel_id) {
    global $wpdb;
    $users_with_bookmark = array();
    $all_users = $wpdb->get_results(
        "SELECT user_id, meta_value FROM $wpdb->usermeta WHERE meta_key = '_novel_bookmarks'"
    );
    foreach ($all_users as $user) {
        $bookmarks = json_decode($user->meta_value, true);
        if (is_array($bookmarks) && in_array($novel_id, $bookmarks)) {
            $users_with_bookmark[] = $user->user_id;
        }
    }
    if (empty($users_with_bookmark)) return;

    $novel_title   = get_the_title($novel_id);
    $chapter_title = get_the_title($chapter_id);

    foreach ($users_with_bookmark as $user_id) {
        $message = sprintf('تم إضافة فصل جديد "%s" للرواية "%s"', $chapter_title, $novel_title);
        $title = 'فصل جديد متاح!';
        sekai_add_notification($user_id, 'new_chapter', $message, $title);
    }
}