<!DOCTYPE html>
<html <?php language_attributes(); ?> class="h-100">
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#0d6efd">
    <meta name="description" content="<?php bloginfo('description'); ?>">

    <!-- تحميل الخطوط بشكل أمثل -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Noto+Kufi+Arabic:wght@400;500;700&family=Noto+Naskh+Arabic:wght@400;500;700&display=swap" as="style">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Noto+Kufi+Arabic:wght@400;500;700&family=Noto+Naskh+Arabic:wght@400;500;700&display=swap">

    <!-- تحميل Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" as="style">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">

    <!-- أنماط أساسية -->
    <style>
    :root {
        /* ألوان أساسية */
        --primary-color: #0d6efd;
        --primary-hover: #0b5ed7;
        --secondary-color: #6c757d;

        /* ألوان الخلفية والنص للوضع الفاتح */
        --bg-color: #ffffff;
        --text-color: #212529;
        --light-bg: #f8f9fa;
        --border-color: rgba(0, 0, 0, 0.1);

        /* ألوان الوضع المظلم */
        --dark-bg: #212529;
        --dark-text: #f8f9fa;
        --dark-border: rgba(255, 255, 255, 0.1);

        /* متغيرات أخرى */
        --transition: all 0.3s ease;
        --border-radius: 0.5rem;
        --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* تطبيق الألوان في الوضع الفاتح */
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
        transition: background-color 0.5s ease, color 0.5s ease;
    }

    /* تطبيق الألوان في الوضع المظلم */
    body.dark-mode {
        --bg-color: #121212;
        --text-color: #f8f9fa;
        --border-color: var(--dark-border);
        --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    /* تنسيق الشعار */
    .custom-logo-link img {
        max-width: 250px;
        height: auto;
        width: auto;
        transition: var(--transition);
    }

    @media (max-width: 768px) {
        .custom-logo-link img {
            max-width: 180px; /* حجم أصغر للشاشات المتوسطة */
        }
    }

    @media (max-width: 480px) {
        .custom-logo-link img {
            max-width: 150px; /* حجم أصغر للشاشات الصغيرة */
        }
    }
    </style>

    <?php wp_head(); ?>
</head>

<body <?php body_class('d-flex flex-column h-100'); ?>>
<?php wp_body_open(); ?>

<?php
// تضمين ملف نظام الإشعارات
if (is_user_logged_in() && !defined('NOTIFICATIONS_LOADED')) {
    define('NOTIFICATIONS_LOADED', true);
    require_once get_template_directory() . '/inc/load-notifications.php';

    // التأكد من تحميل وظيفة عدد الإشعارات غير المقروءة
    if (!function_exists('get_user_unread_notifications_count')) {
        function get_user_unread_notifications_count() {
            $user_id = get_current_user_id();
            if (!$user_id) return 0;

            global $wpdb;
            $table_name = $wpdb->prefix . 'sekai_notifications';

            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND is_read = 0",
                $user_id
            ));

            return intval($count);
        }
    }
}

// تحضير متغيرات المستخدم والإشعارات
$current_user = wp_get_current_user();
$unread_count = function_exists('get_user_unread_notifications_count') ? get_user_unread_notifications_count() : 0;
?>

<!-- Navbar -->
<nav class="navbar navbar-expand-lg sticky-top">
    <div class="container">
        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="تبديل القائمة">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Brand -->
        <a class="navbar-brand" href="<?php echo esc_url(home_url('/')); ?>">
            <?php if (has_custom_logo()): ?>
                <?php the_custom_logo(); ?>
            <?php else: ?>
                <span class="site-title"><?php bloginfo('name'); ?></span>
            <?php endif; ?>
        </a>

        <!-- Main Navigation -->
        <div class="collapse navbar-collapse" id="mainNavbar">
            <div class="navbar-nav-center">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container' => false,
                    'menu_class' => 'navbar-nav',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
                    'depth' => 2,
                    'walker' => new Sekaiplus_Menu_Walker()
                ));
                ?>

                <!-- Search Button -->
                <div class="nav-item search-toggle-wrapper">
                    <button type="button" class="btn btn-icon search-toggle" data-bs-toggle="modal" data-bs-target="#quickSearchModal">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- Actions -->
            <div class="navbar-actions">
                <!-- Dark Mode Toggle - Always visible -->
                <button type="button" class="btn btn-icon" id="darkModeToggle" aria-label="تبديل الوضع المظلم">
                    <div class="toggle-icons-wrapper">
                        <i class="fas fa-moon"></i>
                        <i class="fas fa-sun"></i>
                    </div>
                </button>

                <?php if (!is_user_logged_in()): ?>
                    <!-- Login/Register Buttons for Guests -->
                    <div class="auth-buttons">
                        <?php
                        // تحضير روابط تسجيل الدخول والتسجيل
                        $login_page = get_page_by_path('login');
                        $login_url = $login_page ? get_permalink($login_page->ID) : wp_login_url();

                        $register_page = get_page_by_path('register');
                        $register_url = $register_page ? get_permalink($register_page->ID) : wp_registration_url();
                        ?>
                        <a href="<?php echo esc_url($login_url); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span class="d-none d-md-inline">تسجيل الدخول</span>
                        </a>
                        <a href="<?php echo esc_url($register_url); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user-plus me-1"></i>
                            <span class="d-none d-md-inline">حساب جديد</span>
                        </a>
                    </div>
                <?php else: ?>
                    <!-- Notifications Dropdown for Logged-in Users -->
                    <div class="dropdown notifications-dropdown-wrapper">
                        <button type="button" class="btn btn-icon" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <?php if ($unread_count > 0): ?>
                                <span class="badge bg-danger"><?php echo $unread_count; ?></span>
                            <?php endif; ?>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end notifications-dropdown">
                            <div class="notifications-header">
                                <h6 class="dropdown-header">الإشعارات</h6>
                                <?php if ($unread_count > 0): ?>
                                    <button type="button" class="btn btn-sm btn-link mark-all-read">
                                        <i class="fas fa-check-double"></i> تعيين الكل كمقروء
                                    </button>
                                <?php endif; ?>
                            </div>
                            <div class="notifications-list">
                            <?php
                            $notifications = get_user_notifications(5);
                            if (!empty($notifications)):
                                foreach ($notifications as $notification):
                                    // تحديد الأيقونة ولون الشارة بناءً على نوع الإشعار
                                    $icon_class = 'fa-bell';
                                    $badge_class = 'bg-primary';

                                    if ($notification->type === 'new_chapter') {
                                        $icon_class = 'fa-book-open';
                                        $badge_class = 'bg-success';
                                    } elseif ($notification->type === 'approved_content') {
                                        $icon_class = 'fa-check-circle';
                                        $badge_class = 'bg-info';
                                    }

                                    // تحديد رابط الإشعار
                                    $notification_link = '#';
                                    if ($notification->reference_id && $notification->reference_type) {
                                        if (in_array($notification->reference_type, array('novel', 'chapter'))) {
                                            $notification_link = get_permalink($notification->reference_id);
                                        }
                                    }
                            ?>
                                <a href="<?php echo esc_url($notification_link); ?>" class="dropdown-item notification-item <?php echo $notification->is_read ? '' : 'unread'; ?>" data-notification-id="<?php echo $notification->id; ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="notification-icon">
                                            <span class="badge rounded-circle <?php echo $badge_class; ?>">
                                                <i class="fas <?php echo $icon_class; ?>"></i>
                                            </span>
                                        </div>
                                        <div class="ms-2">
                                            <div class="notification-content"><?php echo esc_html($notification->content); ?></div>
                                            <div class="notification-time text-muted small">
                                                <i class="far fa-clock"></i> <?php echo human_time_diff(strtotime($notification->created_at), current_time('timestamp')); ?> <?php _e('منذ', 'sekaiplus'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            <?php
                                endforeach;
                            else:
                            ?>
                                <div class="dropdown-item text-center">
                                    <p class="text-muted mb-0">لا توجد إشعارات</p>
                                </div>
                            <?php endif; ?>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a href="<?php echo esc_url(home_url('/notifications/')); ?>" class="dropdown-item text-center view-all">
                                عرض جميع الإشعارات
                                <i class="fas fa-chevron-left ms-1"></i>
                            </a>
                        </div>
                    </div>

                    <!-- User Menu Dropdown -->
                    <div class="dropdown user-dropdown-wrapper">
                        <button class="btn btn-icon user-menu-button" type="button" id="userMenuDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <?php echo get_avatar($current_user->ID, 32); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li class="dropdown-header">
                                <div class="user-info">
                                    <?php echo get_avatar($current_user->ID, 48); ?>
                                    <div class="user-details">
                                        <div class="user-name"><?php echo esc_html($current_user->display_name); ?></div>
                                        <div class="user-email text-muted"><?php echo esc_html($current_user->user_email); ?></div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo esc_url(home_url('/u/' . urlencode($current_user->user_nicename))); ?>">
                                    <i class="fas fa-user me-2"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo esc_url(home_url('/bookmarks')); ?>">
                                    <i class="fas fa-bookmark me-2"></i> المفضلة
                                </a>
                            </li>
                            <?php if (current_user_can('manage_options') || current_user_can('edit_posts')): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo esc_url(home_url('/wp-admin')); ?>">
                                        <i class="fas fa-cog me-2"></i> لوحة التحكم
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if (current_user_can('translate_posts')): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo esc_url(home_url('/translator-dashboard')); ?>">
                                        <i class="fas fa-language me-2"></i> لوحة تحكم المترجمين
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo esc_url(wp_logout_url(home_url())); ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<style>
/* ===== أنماط شريط التنقل ===== */
.navbar {
    background-color: var(--light-bg);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
    transition: var(--transition);
}

.dark-mode .navbar {
    background-color: var(--dark-bg);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.site-title {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    color: var(--primary-color);
    background-color: transparent;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

/* ===== قائمة التنقل ===== */
.navbar-nav-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
}

/* تأكيد لون النص في القائمة الرئيسية في الوضع الفاتح */
body:not(.dark-mode) .navbar-nav-center ul.navbar-nav li a {
    color: var(--secondary-color) !important;
}

body:not(.dark-mode) .navbar-nav-center ul.navbar-nav li a:hover {
    color: var(--primary-color) !important;
}

.navbar-nav {
    display: flex;
    gap: 0.5rem;
}

.navbar-nav .nav-item {
    position: relative;
}

.navbar-nav .nav-link {
    color: var(--secondary-color);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

/* تأكيد لون النص في الوضع الفاتح */
body:not(.dark-mode) .navbar-nav .nav-link,
body:not(.dark-mode) .navbar-nav-center .menu-item a {
    color: var(--secondary-color) !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: var(--primary-color);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

.navbar-nav .dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.navbar-nav .dropdown-item {
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
}

.navbar-nav .dropdown-item:hover,
.navbar-nav .dropdown-item:focus {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    color: var(--primary-color);
}

/* ===== زر البحث ===== */
.search-toggle-wrapper {
    margin-right: 0.5rem;
}

.search-toggle {
    color: var(--secondary-color);
    transition: var(--transition);
}

/* تأكيد لون زر البحث في الوضع الفاتح */
body:not(.dark-mode) .search-toggle {
    color: var(--secondary-color) !important;
}

.search-toggle:hover {
    color: var(--primary-color);
    transform: scale(1.05);
}

/* ===== أزرار الإجراءات ===== */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: var(--secondary-color);
    border: none;
    transition: var(--transition);
    position: relative;
}

.btn-icon:hover {
    color: var(--primary-color);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    transform: scale(1.05);
}

/* ===== أزرار تسجيل الدخول والتسجيل ===== */
.auth-buttons {
    display: flex;
    gap: 0.5rem;
}

/* ===== زر الوضع المظلم ===== */
#darkModeToggle {
    position: relative;
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--secondary-color);
    overflow: hidden;
}

#darkModeToggle:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    transform: scale(1.05);
}

.toggle-icons-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تنسيق أيقونات الوضع الداكن والفاتح */
#darkModeToggle .fa-moon {
    display: block;
    color: #6c757d;
    transition: all 0.3s ease;
}

#darkModeToggle .fa-sun {
    display: none;
    color: #ffc107;
    transition: all 0.3s ease;
}

/* تغيير الأيقونات في الوضع المظلم */
.dark-mode #darkModeToggle .fa-moon {
    display: none;
}

.dark-mode #darkModeToggle .fa-sun {
    display: block;
}

/* تحسين مظهر الأيقونات عند التحويم */
#darkModeToggle:hover .fa-moon {
    color: #495057;
}

#darkModeToggle:hover .fa-sun {
    color: #ffcd39;
}

/* ===== تنسيق الإشعارات ===== */
.notifications-dropdown-wrapper {
    position: relative;
}

.notifications-dropdown {
    width: 350px;
    max-height: 450px;
    overflow: hidden;
    padding: 0;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.notifications-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .notifications-header {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

.notifications-header .dropdown-header {
    padding: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.mark-all-read {
    font-size: 0.8rem;
    padding: 0;
    color: var(--primary-color);
}

.mark-all-read:hover {
    text-decoration: underline;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.notifications-list::-webkit-scrollbar {
    width: 4px;
}

.notifications-list::-webkit-scrollbar-track {
    background: transparent;
}

.notifications-list::-webkit-scrollbar-thumb {
    background-color: rgba(var(--bs-primary-rgb), 0.2);
    border-radius: 4px;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.dark-mode .notification-item {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

.notification-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.notification-item.unread {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-right: 3px solid var(--primary-color);
}

.notification-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-icon .badge {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-content {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.notification-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.view-all {
    font-weight: 500;
    color: var(--primary-color) !important;
    padding: 0.75rem !important;
    transition: var(--transition);
}

.view-all:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05) !important;
}

/* تنسيق زر الإشعارات */
#notificationsDropdown .badge {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 0.65rem;
    padding: 0.2rem 0.4rem;
    transform: translate(25%, -25%);
}

/* ===== قائمة المستخدم ===== */
.user-dropdown-wrapper {
    position: relative;
}

.user-menu-button {
    padding: 0;
    overflow: hidden;
    border: 2px solid transparent;
}

.user-menu-button:hover {
    border-color: var(--primary-color);
}

.user-menu-button img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.user-dropdown {
    width: 280px;
    padding: 0;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.user-dropdown .dropdown-header {
    padding: 1rem;
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .user-dropdown .dropdown-header {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-info img {
    border-radius: 50%;
    border: 2px solid var(--primary-color);
}

.user-details {
    flex: 1;
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.user-dropdown .dropdown-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.user-dropdown .dropdown-item i {
    width: 1.25rem;
    text-align: center;
}

.user-dropdown .text-danger:hover {
    background-color: rgba(var(--bs-danger-rgb), 0.05) !important;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: var(--light-bg);
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 1rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

    .dark-mode .navbar-collapse {
        background-color: var(--dark-bg);
    }

    .navbar-nav-center {
        flex-direction: column;
        align-items: flex-start;
    }

    .navbar-nav {
        flex-direction: column;
        width: 100%;
    }

    .search-toggle-wrapper {
        margin: 0.5rem 0;
    }
}

@media (max-width: 767.98px) {
    .notifications-dropdown {
        width: 300px;
    }

    .user-dropdown {
        width: 250px;
    }
}

@media (max-width: 575.98px) {
    .navbar-actions {
        gap: 0.5rem;
    }

    .btn-icon {
        width: 2.25rem;
        height: 2.25rem;
    }

    .notifications-dropdown {
        width: 280px;
        position: fixed !important;
        left: 1rem !important;
        right: 1rem !important;
        width: auto !important;
    }

    .user-dropdown {
        width: 230px;
    }
}
</style>

<!-- Quick Search Modal -->
<div class="modal fade" id="quickSearchModal" tabindex="-1" aria-labelledby="quickSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickSearchModalLabel">
                    <i class="fas fa-search me-2"></i>
                    البحث السريع
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="search-wrapper position-relative">
                    <div class="input-group input-group-lg search-input-group">
                        <span class="input-group-text bg-transparent border-end-0">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="quickSearch" class="form-control border-start-0"
                               placeholder="ابحث عن رواية، فصل، مؤلف..." autocomplete="off">
                        <button type="button" class="btn btn-outline-secondary clear-search" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="search-filters mt-2 mb-3">
                        <div class="btn-group" role="group" aria-label="فلاتر البحث">
                            <input type="radio" class="btn-check" name="searchType" id="searchAll" value="all" checked>
                            <label class="btn btn-outline-primary btn-sm" for="searchAll">الكل</label>

                            <input type="radio" class="btn-check" name="searchType" id="searchNovels" value="novels">
                            <label class="btn btn-outline-primary btn-sm" for="searchNovels">الروايات</label>

                            <input type="radio" class="btn-check" name="searchType" id="searchChapters" value="chapters">
                            <label class="btn btn-outline-primary btn-sm" for="searchChapters">الفصول</label>

                            <input type="radio" class="btn-check" name="searchType" id="searchAuthors" value="authors">
                            <label class="btn btn-outline-primary btn-sm" for="searchAuthors">المؤلفين</label>
                        </div>
                    </div>

                    <div id="searchResults" class="quick-search-results shadow-lg"></div>

                    <div class="search-loading" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                    </div>

                    <div class="search-empty text-center p-4" style="display: none;">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="lead">لم يتم العثور على نتائج</p>
                        <p class="text-muted">حاول استخدام كلمات بحث مختلفة أو تغيير فلتر البحث</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="<?php echo esc_url(home_url('/search/')); ?>" class="btn btn-link">البحث المتقدم</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
/* ===== أنماط البحث السريع ===== */
#quickSearchModal .modal-content {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.dark-mode #quickSearchModal .modal-content {
    background-color: var(--dark-bg);
    color: var(--light-bg);
}

.dark-mode #quickSearchModal .modal-header,
.dark-mode #quickSearchModal .modal-footer {
    border-color: rgba(255, 255, 255, 0.1);
}

.search-input-group .input-group-text,
.search-input-group .form-control {
    background-color: var(--light-bg);
    border-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .search-input-group .input-group-text,
.dark-mode .search-input-group .form-control {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--light-bg);
}

.search-input-group .form-control:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.dark-mode .search-input-group .form-control:focus {
    border-color: var(--primary-color);
}

.search-input-group .input-group-text {
    color: var(--secondary-color);
}

.search-filters {
    display: flex;
    justify-content: center;
}

.search-filters .btn-group {
    flex-wrap: wrap;
    justify-content: center;
}

.search-filters .btn {
    border-radius: 2rem;
    margin: 0.25rem;
    padding: 0.375rem 0.75rem;
}

.quick-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--light-bg);
    border-radius: 0.5rem;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    display: none;
}

.dark-mode .quick-search-results {
    background-color: var(--dark-bg);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    text-decoration: none;
    color: var(--secondary-color);
}

.dark-mode .search-result-item {
    border-bottom-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
}

.search-result-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.search-result-image {
    width: 50px;
    height: 70px;
    border-radius: 0.25rem;
    overflow: hidden;
    margin-left: 1rem;
    flex-shrink: 0;
}

.search-result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.search-result-content {
    flex: 1;
}

.search-result-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.search-result-meta {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.dark-mode .search-result-meta {
    color: rgba(255, 255, 255, 0.6);
}

.search-result-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 2rem;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.search-result-type.novel {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--primary-color);
}

.search-result-type.chapter {
    background-color: rgba(var(--bs-success-rgb), 0.1);
    color: var(--bs-success);
}

.search-result-type.author {
    background-color: rgba(var(--bs-info-rgb), 0.1);
    color: var(--bs-info);
}

.search-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
}

@media (max-width: 767.98px) {
    .search-result-image {
        width: 40px;
        height: 60px;
    }

    .search-filters .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>


<main class="flex-shrink-0">
