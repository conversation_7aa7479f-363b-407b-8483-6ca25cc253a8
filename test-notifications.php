<?php
/**
 * ملف اختبار نظام الإشعارات
 * يمكن الوصول إليه عبر: yoursite.com/wp-content/themes/Sekaiplus/test-notifications.php
 */

// تحميل WordPress
require_once('../../../wp-load.php');

// التحقق من تسجيل الدخول
if (!is_user_logged_in()) {
    die('يجب تسجيل الدخول أولاً');
}

echo '<h1>اختبار نظام الإشعارات</h1>';

// التحقق من وجود الجدول
global $wpdb;
$table_name = $wpdb->prefix . 'sekaiplus_notifications';

echo '<h2>1. التحقق من وجود الجدول:</h2>';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
echo $table_exists ? '✅ الجدول موجود' : '❌ الجدول غير موجود';
echo '<br>';

if (!$table_exists) {
    echo '<h3>إنشاء الجدول...</h3>';
    sekai_create_notifications_table();
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    echo $table_exists ? '✅ تم إنشاء الجدول بنجاح' : '❌ فشل في إنشاء الجدول';
    echo '<br>';
}

// التحقق من الوظائف
echo '<h2>2. التحقق من الوظائف:</h2>';
echo function_exists('sekai_add_notification') ? '✅ sekai_add_notification موجودة' : '❌ sekai_add_notification غير موجودة';
echo '<br>';
echo function_exists('sekai_get_user_notifications') ? '✅ sekai_get_user_notifications موجودة' : '❌ sekai_get_user_notifications غير موجودة';
echo '<br>';
echo function_exists('get_user_notifications') ? '✅ get_user_notifications موجودة' : '❌ get_user_notifications غير موجودة';
echo '<br>';
echo function_exists('get_user_unread_notifications_count') ? '✅ get_user_unread_notifications_count موجودة' : '❌ get_user_unread_notifications_count غير موجودة';
echo '<br>';

// إضافة إشعار تجريبي
if (isset($_GET['add_test'])) {
    echo '<h2>3. إضافة إشعار تجريبي:</h2>';
    $result = sekai_add_notification(
        get_current_user_id(),
        'system',
        'هذا إشعار تجريبي من ملف الاختبار - ' . date('Y-m-d H:i:s'),
        0,
        'test'
    );
    echo $result ? '✅ تم إضافة الإشعار بنجاح (ID: ' . $result . ')' : '❌ فشل في إضافة الإشعار';
    echo '<br>';
}

// عرض الإشعارات
echo '<h2>4. الإشعارات الحالية:</h2>';
$user_id = get_current_user_id();
$notifications = sekai_get_user_notifications($user_id, 10, 0, false);

if (empty($notifications)) {
    echo 'لا توجد إشعارات';
} else {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>ID</th><th>النوع</th><th>المحتوى</th><th>مقروء</th><th>التاريخ</th></tr>';
    foreach ($notifications as $notification) {
        echo '<tr>';
        echo '<td>' . $notification->id . '</td>';
        echo '<td>' . $notification->type . '</td>';
        echo '<td>' . $notification->content . '</td>';
        echo '<td>' . ($notification->is_read ? 'نعم' : 'لا') . '</td>';
        echo '<td>' . $notification->created_at . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// عدد الإشعارات غير المقروءة
echo '<h2>5. عدد الإشعارات غير المقروءة:</h2>';
$unread_count = get_user_unread_notifications_count();
echo 'العدد: ' . $unread_count;
echo '<br>';

// روابط الاختبار
echo '<h2>6. روابط الاختبار:</h2>';
echo '<a href="?add_test=1">إضافة إشعار تجريبي</a><br>';
echo '<a href="' . home_url() . '">العودة للصفحة الرئيسية</a><br>';

// معلومات إضافية
echo '<h2>7. معلومات إضافية:</h2>';
echo 'اسم الجدول: ' . $table_name . '<br>';
echo 'معرف المستخدم: ' . get_current_user_id() . '<br>';
echo 'WordPress AJAX URL: ' . admin_url('admin-ajax.php') . '<br>';

// التحقق من JavaScript
echo '<h2>8. اختبار JavaScript:</h2>';
echo '<button id="test-ajax">اختبار AJAX</button>';
echo '<div id="ajax-result"></div>';

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
jQuery(document).ready(function($) {
    $('#test-ajax').click(function() {
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'get_unread_notifications_count',
                nonce: '<?php echo wp_create_nonce('sekai_ajax_nonce'); ?>'
            },
            success: function(response) {
                $('#ajax-result').html('نجح AJAX: ' + JSON.stringify(response));
            },
            error: function() {
                $('#ajax-result').html('فشل AJAX');
            }
        });
    });
});
</script>
