/**
 * ملف JavaScript للتعامل مع نظام الإشعارات
 */
jQuery(document).ready(function($) {
    // حذف إشعار واحد
    $(document).on('click', '.delete-notification', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
            return;
        }
        
        var notificationId = $(this).data('id');
        var notificationItem = $(this).closest('.notification-item');
        
        $.ajax({
            url: sekai_ajax_obj.ajax_url, // استخدام متغير sekai_ajax_obj.ajax_url بدلاً من ajaxurl
            type: 'POST',
            data: {
                action: 'delete_notification',
                notification_id: notificationId,
                nonce: sekai_ajax_obj.nonce
            },
            success: function(response) {
                if (response.success) {
                    notificationItem.fadeOut(300, function() {
                        $(this).remove();
                        
                        // تحديث عدد الإشعارات
                        updateNotificationsCount();
                        
                        // إذا لم يعد هناك إشعارات
                        if ($('.notification-item').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(response.data.message || 'حدث خطأ أثناء حذف الإشعار');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء معالجة الطلب');
            }
        });
    });
    
    // حذف جميع الإشعارات
    $('#clearAllNotifications').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
            return;
        }
        
        $.ajax({
            url: sekai_ajax_obj.ajax_url, // استخدام متغير sekai_ajax_obj.ajax_url بدلاً من ajaxurl
            type: 'POST',
            data: {
                action: 'delete_all_notifications',
                nonce: sekai_ajax_obj.nonce
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data.message || 'حدث خطأ أثناء حذف الإشعارات');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء معالجة الطلب');
            }
        });
    });
    
    // تحديث عدد الإشعارات غير المقروءة
    function updateNotificationsCount() {
        $.ajax({
            url: sekai_ajax_obj.ajax_url, // استخدام متغير sekai_ajax_obj.ajax_url بدلاً من ajaxurl
            type: 'POST',
            data: {
                action: 'get_unread_notifications_count',
                nonce: sekai_ajax_obj.nonce
            },
            success: function(response) {
                if (response.success) {
                    var count = parseInt(response.data.count);
                    var badge = $('#notificationsDropdown .badge');
                    
                    if (count > 0) {
                        if (badge.length) {
                            badge.text(count);
                        } else {
                            $('#notificationsDropdown').append('<span class="badge bg-danger">' + count + '</span>');
                        }
                    } else {
                        badge.remove();
                    }
                }
            }
        });
    }
    
    // عند النقر على إشعار غير مقروء
    $(document).on('click', '.notification-item.unread', function(e) {
        var notificationId = $(this).data('id');
        
        // لا تقم بالعملية إذا تم النقر على زر الحذف
        if ($(e.target).closest('.delete-notification').length) {
            return;
        }
        
        // تحديث حالة القراءة
        $.ajax({
            url: sekai_ajax_obj.ajax_url, // استخدام متغير sekai_ajax_obj.ajax_url بدلاً من ajaxurl
            type: 'POST',
            data: {
                action: 'mark_notification_as_read',
                notification_id: notificationId,
                nonce: sekai_ajax_obj.nonce
            },
            success: function(response) {
                if (response.success) {
                    // إزالة فئة غير مقروء
                    $('.notification-item[data-id="' + notificationId + '"]').removeClass('unread');
                    
                    // تحديث عدد الإشعارات
                    updateNotificationsCount();
                }
            }
        });
    });
});
