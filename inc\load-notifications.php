<?php
/**
 * ملف تحميل نظام الإشعارات
 * 
 * هذا الملف يقوم بتحميل نظام الإشعارات بشكل منفصل عن functions.php
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تضمين ملفات نظام الإشعارات
require_once get_template_directory() . '/inc/notifications-system.php';
require_once get_template_directory() . '/inc/notifications-ajax.php';

/**
 * تسجيل سكريبت الإشعارات
 */
function sekai_register_notifications_scripts() {
    wp_register_script(
        'sekai-notifications', 
        get_template_directory_uri() . '/assets/js/notifications.js', 
        array('jquery'), 
        '1.0.1', 
        true
    );
    
    // إضافة nonce للتحقق من الأمان في طلبات AJAX
    wp_localize_script('sekai-notifications', 'sekai_ajax_obj', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sekai_ajax_nonce'),
    ));
    
    wp_enqueue_script('sekai-notifications');
}
add_action('wp_enqueue_scripts', 'sekai_register_notifications_scripts');

/**
 * إضافة الوظائف المطلوبة للواجهة
 */
function get_user_notifications($limit = 10) {
    if (!is_user_logged_in()) {
        return array();
    }
    
    $user_id = get_current_user_id();
    return sekai_get_user_notifications($user_id, $limit, 0, false);
}

// ملاحظة: تم إزالة تعريف الدالة get_user_unread_notifications_count() لتجنب التعارض مع functions.php
